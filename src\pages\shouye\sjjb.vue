<template>
  <view>
    <!-- Tab内容区域 -->
    <view
      class="tab-content"
      style="padding-bottom: 40rpx"
      ref="pdfContent"
      id="pdfContent"
    >
      <view class="indicator-card">
        <view class="card-header flex-b">
          <view class="flex-c">
            <image src="@/static/img/home/<USER>" class="dot" />
            <text class="card-title">AI概览</text>
          </view>
          <view class="exportBtn" @click="exportRibao">导出简报</view>
        </view>
        <view class="aiReport">
          <u-parse
            ref="aiReport"
            :content="thinkingContent"
            class="parseClass"
          ></u-parse>
        </view>
      </view>

      <view class="indicator-card">
        <view class="card-header">
          <image src="@/static/img/home/<USER>" class="dot" />
          <text class="card-title">每日摘要</text>
        </view>
        <!-- 经济 -->
        <view class="card" v-if="textList1.length > 0">
          <view class="header" :style="{ backgroundColor: '#0098fa1a' }">
            <view class="icon-container">
              <image src="@/static/img/home/<USER>" class="iconfont" />
            </view>
            <text class="title">经济</text>
          </view>
          <view class="textList">
            <view class="textItem" v-for="(item, i) in textList1" :key="i">
              <view class="divider" v-if="i !== 0"></view>
              <u-parse :content="item"></u-parse>
            </view>
          </view>
          <!-- <view style="width: 100%; height: 330rpx">
            <LEchart ref="lineChart" style="width: 100%; height: 100%" />
          </view> -->
        </view>
        <!-- 民生 -->
        <view class="card" v-if="textList2.length > 0">
          <view class="header" :style="{ backgroundColor: '#F276291A' }">
            <view class="icon-container">
              <image src="@/static/img/home/<USER>" class="iconfont" />
            </view>
            <text class="title">民生</text>
          </view>
          <view class="textList">
            <view class="textItem" v-for="(item, i) in textList2" :key="i">
              <view class="divider" v-if="i !== 0"></view>
              <u-parse :content="item"></u-parse>
            </view>
          </view>
        </view>
        <!-- 环境 -->
        <view class="card" v-if="textList3.length > 0">
          <view class="header" :style="{ backgroundColor: '#0CD9B51A' }">
            <view class="icon-container">
              <image src="@/static/img/home/<USER>" class="iconfont" />
            </view>
            <text class="title">环境</text>
          </view>
          <view class="textList">
            <view class="textItem" v-for="(item, i) in textList3" :key="i">
              <view class="divider" v-if="i !== 0"></view>
              <u-parse :content="item"></u-parse>
            </view>
          </view>
        </view>
        <!-- 平安 -->
        <view class="card" v-if="textList4.length > 0">
          <view class="header" :style="{ backgroundColor: '#FDCC001A' }">
            <view class="icon-container">
              <image src="@/static/img/home/<USER>" class="iconfont" />
            </view>
            <text class="title">平安</text>
          </view>
          <view class="textList">
            <view class="textItem" v-for="(item, i) in textList4" :key="i">
              <view class="divider" v-if="i !== 0"></view>
              <u-parse :content="item"></u-parse>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import html2canvas from 'html2canvas'
import { jsPDF } from 'jspdf'
import * as echarts from '@/pages/shouye/components/lime-echart/static/echarts.min.js'
import LEchart from '@/pages/shouye/components/lime-echart/components/l-echart/l-echart.vue'
import { getCsdnInterface } from '@/services/csdnIndexApi/index.js'

export default {
  components: {
    LEchart,
  },
  props: {},
  watch: {},
  data() {
    return {
      list: [
        { name: '经济', color: '#0098FA', text: '表现优秀（指数≥90）' },
        { name: '民生', color: '#0CD9B5', text: '正常、良好（90>指数≥80）' },
        { name: '平安', color: '#FDCC00', text: '小幅度异常（80>指数≥60）' },
        { name: '环境', color: '#F27629', text: '大幅度异常（60>指数≥0）' },
      ],
      textList1: '',
      textList2: '',
      textList3: '',
      textList4: '',
      //大模型
      thinkingContent: '', //接口返回的answer
      //导出
      posterUrl:
        'https://csdn.dsjj.jinhua.gov.cn:8101/taskReply/fileManage/%E5%9F%8E%E5%B8%82%E5%A4%A7%E8%84%91%E6%97%A5%E6%8A%A5_0707.docx',
    }
  },
  filters: {
    addDot(value) {
      return
    },
  },
  mounted() {
    this.getDayInfo()
    this.getAiReport2()
  },
  methods: {
    async getDayInfo() {
      getCsdnInterface('ldrb_mrzy').then((res) => {
        if (res.responsecode == 200) {
          let content = res.data[0].content
          
        }
      })
    },
    async getAiReport2() {
      //每日更新的数据库,模拟流式
      getCsdnInterface('ldrb_aigl').then((res) => {
        if (res.responsecode == 200) {
          this.thinkingContent = res.data[0].content
          // this.paragraph = res.data[0].content
          // // console.log(this.paragraph);
          // // this.paragraph = this.paragraph.replaceAll('<think', '<p')
          // this.paragraph = this.paragraph.replaceAll('############', '')
          // const arr1 = this.paragraph.split('<think>')
          // const arr11 = arr1[1].split('</think>') //获取周报数据比对arr11[1]
          // const arr12 = arr1[2].split('</think>') //获取用电量和气温变化关系分析arr12[1]
          // this.thinkingContent =
          //   '<p>' + arr11[0] + '</p>\n<p>' + arr12[0] + '</p>'
          // this.paragraph = '<p>' + arr11[1] + '</p>\n<p>' + arr12[1] + '</p>'
        }
      })
    },
    exportRibao() {
      var a = document.createElement('a')
      const url = this.posterUrl
      a.href = url
      a.click()
      window.URL.revokeObjectURL(url)
    },
    textAddDot(list) {
      list.forEach((item, i) => {
        list[i] = list[i].replaceAll(
          'dot_blue',
          '<view style="width: 15rpx;height: 15rpx;border-radius: 50%;background-color: #0098fa;margin:0rpx 10rpx 2rpx 10rpx;display: inline-block;"></view>'
        )
        list[i] = list[i].replaceAll(
          'dot_green',
          '<view style="width: 15rpx;height: 15rpx;border-radius: 50%;background-color: #0CD9B5;margin:0rpx 10rpx 2rpx 10rpx;display: inline-block;"></view>'
        )
        list[i] = list[i].replaceAll(
          'dot_yellow',
          '<view style="width: 15rpx;height: 15rpx;border-radius: 50%;background-color: #FDCC00;margin:0rpx 10rpx 2rpx 10rpx;display: inline-block;"></view>'
        )
        list[i] = list[i].replaceAll(
          'dot_red',
          '<view style="width: 15rpx;height: 15rpx;border-radius: 50%;background-color: #F27629;margin:0rpx 10rpx 2rpx 10rpx;display: inline-block;"></view>'
        )
      })
      this.$forceUpdate()
    },
  },
}
</script>

<style lang="scss" scoped>
.indicator-card {
  margin: 0 25rpx;

  .card-header {
    display: flex;
    align-items: center;
    margin-bottom: 30rpx;

    .dot {
      width: 24rpx;
      height: 14rpx;
      margin-right: 16rpx;
    }

    .card-title {
      font-size: 32rpx;
      font-weight: bold;
      color: #333;
    }
    .exportBtn {
      padding: 8rpx 20rpx;
      background-color: #fff;
      border-radius: 8rpx;
      font-size: 28rpx;
    }
  }
  .card {
    background-color: #fff;
    border-radius: 16rpx;
    padding: 20rpx;
    box-sizing: border-box;
    margin-bottom: 30rpx;
  }
  .header {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 30rpx 20rpx;
    border-radius: 10rpx;

    .icon-container {
      border-radius: 30rpx;
      display: flex;
      justify-content: center;
      align-items: center;
      margin-right: 20rpx;

      .iconfont {
        width: 60rpx;
        height: 60rpx;
      }
    }

    .title {
      font-size: 40rpx;
      font-weight: bold;
      color: #333;
    }
  }
}
.listCon {
  padding: 20rpx 40rpx;
  box-sizing: border-box;
  font-size: 36rpx;
  .icon {
    width: 15rpx;
    height: 15rpx;
    border-radius: 50%;
    margin-right: 14rpx;
  }
  .listItem {
    font-family: Source Han Sans, Source Han Sans;
    font-weight: 400;
    font-size: 30rpx;
    color: #666666;
    line-height: 44rpx;
    text-align: left;
    margin: 14rpx 0;
  }
}
.textList {
  padding: 20rpx 0;
  box-sizing: border-box;
  .textItem {
    margin: 20rpx 0;
    font-family: Source Han Sans, Source Han Sans;
    font-weight: 400;
    font-size: 30rpx;
    color: #222222;
    line-height: 43rpx;
    text-align: justify;
    .divider {
      width: 100%;
      height: 1px;
      background-image: linear-gradient(
        to right,
        #cfd2dc 0%,
        #cfd2dc 50%,
        transparent 50%
      );
      background-size: 8px 1px;
      background-repeat: repeat-x;
      margin-bottom: 20rpx;
    }
  }
}
::v-deep .parseClass {
  white-space: pre-wrap;
}
.aiBtn {
  width: 100%;
  height: 90rpx;
}
.aiThinking {
  .thinkHeader {
    font-family: Source Han Sans, Source Han Sans;
    font-weight: 400;
    font-size: 30rpx;
    color: #960cff;
    line-height: 36rpx;
    text-align: left;
    margin-bottom: 10rpx;
    .star {
      width: 24rpx;
      height: 24rpx;
      margin-right: 8rpx;
    }
    .icon {
      width: 22rpx;
      height: 14rpx;
      transition: 0.3s;
    }
    .icon_rotated {
      transform: rotate(180deg);
      transition: 0.3s;
    }
  }
  .thinkContent {
    padding: 0 20rpx;
    box-sizing: border-box;
    font-family: Source Han Sans, Source Han Sans;
    font-weight: 400;
    font-size: 24rpx;
    text-align: justify;
    color: #96a1ad;
    line-height: 36rpx;
    height: auto;
    max-height: 200rpx;
    overflow-y: scroll;
  }
  .thinkContent_all {
    height: unset;
    overflow-y: unset;
    max-height: unset;
  }
}
.aiReport {
  margin-top: 10rpx;
  background-color: #fff;
  border-radius: 16rpx;
  padding: 20rpx;
  box-sizing: border-box;
  margin-bottom: 30rpx;
  font-size: 16px;
  .text_i {
    text-indent: 24px;
    margin-bottom: 12px;
  }
}
</style>
